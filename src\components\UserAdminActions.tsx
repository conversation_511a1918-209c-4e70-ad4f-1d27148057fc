'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ConfirmModal } from '@/components/ui/confirm-modal';
import { useUpdateUserAdmin } from '@/hooks/useMutation';
import {
  IconUserCheck,
  IconUserX,
  IconCrown,
  IconCrownOff,
  IconLock,
  IconLockOpen
} from '@tabler/icons-react';

interface UserAdminActionsProps {
  userId: string;
  userEmail: string;
  isActive: boolean;
  isRestricted?: boolean;
  isProMember?: boolean;
  proTrialEndsAt?: string;
  proMembershipEndsAt?: string;
  onUpdate?: () => void;
}

export function UserAdminActions({
  userId,
  userEmail,
  isActive,
  isRestricted = false,
  isProMember = false,
  proTrialEndsAt,
  proMembershipEndsAt,
  onUpdate
}: UserAdminActionsProps) {
  const [confirmModal, setConfirmModal] = useState<{
    isOpen: boolean;
    title: string;
    description: string;
    action: () => void;
    confirmText: string;
    confirmVariant:
      | 'default'
      | 'destructive'
      | 'outline'
      | 'secondary'
      | 'ghost'
      | 'link';
  }>({
    isOpen: false,
    title: '',
    description: '',
    action: () => {},
    confirmText: 'Confirm',
    confirmVariant: 'default'
  });

  const updateUserMutation = useUpdateUserAdmin({
    onSuccess: () => {
      setConfirmModal((prev) => ({ ...prev, isOpen: false }));
      onUpdate?.();
    }
  });

  const handleToggleActive = () => {
    setConfirmModal({
      isOpen: true,
      title: `${isActive ? 'Deactivate' : 'Activate'} User`,
      description: `Are you sure you want to ${
        isActive ? 'deactivate' : 'activate'
      } ${userEmail}? This will ${
        isActive ? 'prevent' : 'allow'
      } them from accessing the platform.`,
      action: () => {
        updateUserMutation.mutate({
          userId,
          data: { isActive: !isActive }
        });
      },
      confirmText: isActive ? 'Deactivate' : 'Activate',
      confirmVariant: isActive ? 'destructive' : 'default'
    });
  };

  const handleToggleRestricted = () => {
    setConfirmModal({
      isOpen: true,
      title: `${isRestricted ? 'Unrestrict' : 'Restrict'} User`,
      description: `Are you sure you want to ${
        isRestricted ? 'unrestrict' : 'restrict'
      } ${userEmail}? This will ${
        isRestricted ? 'remove restrictions from' : 'add restrictions to'
      } their account.`,
      action: () => {
        updateUserMutation.mutate({
          userId,
          data: { isRestricted: !isRestricted }
        });
      },
      confirmText: isRestricted ? 'Unrestrict' : 'Restrict',
      confirmVariant: isRestricted ? 'default' : 'destructive'
    });
  };

  const handleExtendProTrial = () => {
    const newTrialDate = new Date();
    newTrialDate.setDate(newTrialDate.getDate() + 30); // Extend by 30 days

    setConfirmModal({
      isOpen: true,
      title: 'Extend Pro Trial',
      description: `Are you sure you want to extend the pro trial for ${userEmail} by 30 days?`,
      action: () => {
        updateUserMutation.mutate({
          userId,
          data: { proTrialEndsAt: newTrialDate.toISOString() }
        });
      },
      confirmText: 'Extend Trial',
      confirmVariant: 'default'
    });
  };

  const handleExtendProMembership = () => {
    const newMembershipDate = new Date();
    newMembershipDate.setMonth(newMembershipDate.getMonth() + 1); // Extend by 1 month

    setConfirmModal({
      isOpen: true,
      title: 'Extend Pro Membership',
      description: `Are you sure you want to extend the pro membership for ${userEmail} by 1 month?`,
      action: () => {
        updateUserMutation.mutate({
          userId,
          data: { proMembershipEndsAt: newMembershipDate.toISOString() }
        });
      },
      confirmText: 'Extend Membership',
      confirmVariant: 'default'
    });
  };

  return (
    <>
      <div className='flex flex-wrap gap-2'>
        {/* Status Badges */}
        <Badge variant={isActive ? 'default' : 'destructive'}>
          {isActive ? 'Active' : 'Inactive'}
        </Badge>

        {isRestricted && <Badge variant='destructive'>Restricted</Badge>}

        {isProMember && (
          <Badge variant='secondary' className='bg-yellow-100 text-yellow-800'>
            Pro Member
          </Badge>
        )}

        {/* Action Buttons */}
        <div className='flex gap-1'>
          <Button
            variant={isActive ? 'destructive' : 'default'}
            size='sm'
            onClick={handleToggleActive}
            disabled={updateUserMutation.isPending}
          >
            {isActive ? (
              <>
                <IconUserX className='mr-1 h-3 w-3' />
                Deactivate
              </>
            ) : (
              <>
                <IconUserCheck className='mr-1 h-3 w-3' />
                Activate
              </>
            )}
          </Button>

          <Button
            variant={isRestricted ? 'default' : 'destructive'}
            size='sm'
            onClick={handleToggleRestricted}
            disabled={updateUserMutation.isPending}
          >
            {isRestricted ? (
              <>
                <IconLockOpen className='mr-1 h-3 w-3' />
                Unrestrict
              </>
            ) : (
              <>
                <IconLock className='mr-1 h-3 w-3' />
                Restrict
              </>
            )}
          </Button>

          <Button
            variant='outline'
            size='sm'
            onClick={handleExtendProTrial}
            disabled={updateUserMutation.isPending}
          >
            <IconCrown className='mr-1 h-3 w-3' />
            Extend Trial
          </Button>

          <Button
            variant='outline'
            size='sm'
            onClick={handleExtendProMembership}
            disabled={updateUserMutation.isPending}
          >
            <IconCrownOff className='mr-1 h-3 w-3' />
            Extend Pro
          </Button>
        </div>
      </div>

      <ConfirmModal
        isOpen={confirmModal.isOpen}
        onClose={() => setConfirmModal((prev) => ({ ...prev, isOpen: false }))}
        onConfirm={confirmModal.action}
        title={confirmModal.title}
        description={confirmModal.description}
        confirmText={confirmModal.confirmText}
        confirmVariant={confirmModal.confirmVariant}
      />
    </>
  );
}
